# HireFlow ATS Frontend - Production Docker Image
# Multi-stage build for optimized React application

# ================================
# Stage 1: Build Stage
# ================================
FROM node:20-alpine AS build-stage

# Set working directory
WORKDIR /app

# Install system dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files for better layer caching
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Build the application
RUN npx vite build --config vite.config.client.ts

# ================================
# Stage 2: Production Stage
# ================================
FROM nginx:1.25-alpine AS production

# Install additional tools
RUN apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Create nginx user and group
RUN addgroup -g 1000 -S nginx-app && \
    adduser -u 1000 -S nginx-app -G nginx-app

# Copy built application from build stage
COPY --from=build-stage --chown=nginx-app:nginx-app /app/dist/spa /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf

# Create necessary directories
RUN mkdir -p /var/cache/nginx /var/log/nginx /var/run \
    && chown -R nginx-app:nginx-app /var/cache/nginx /var/log/nginx /var/run \
    && chmod -R 755 /var/cache/nginx /var/log/nginx /var/run

# Create nginx.pid file with correct permissions
RUN touch /var/run/nginx.pid \
    && chown nginx-app:nginx-app /var/run/nginx.pid

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Expose port
EXPOSE 80

# Switch to non-root user
USER nginx-app

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
