# ================================
# HireFlow ATS - Backend Dockerfile
# ================================

# ================================
# Build Stage
# ================================
FROM node:20-alpine AS build-stage

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Build the client (frontend)
RUN npx vite build --config vite.config.client.ts

# ================================
# Production Stage
# ================================
FROM node:20-alpine AS production

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -S app && \
    adduser -S app -G app

# Set working directory
WORKDIR /app

# Copy built application and source files
COPY --from=build-stage --chown=app:app /app/dist /app/dist
COPY --from=build-stage --chown=app:app /app/server /app/server
COPY --from=build-stage --chown=app:app /app/package*.json ./

# Install only production dependencies
RUN npm ci --only=production --legacy-peer-deps && \
    npm cache clean --force

# Create necessary directories
RUN mkdir -p /app/storage/logs && \
    chown -R app:app /app

# Switch to app user
USER app

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/ping || exit 1

# Start the application
CMD ["npx", "tsx", "server/node-build.ts"]
