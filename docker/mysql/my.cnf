[mysqld]
# Basic settings
default-authentication-plugin = mysql_native_password
skip-name-resolve
explicit_defaults_for_timestamp = 1

# Character set and collation
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# Performance settings
max_connections = 200
max_allowed_packet = 64M
thread_cache_size = 8
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# InnoDB settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 50

# Binary logging
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Error log
log-error = /var/log/mysql/error.log

# General log (disable in production)
general_log = 0

# Time zone
default-time-zone = '+07:00'

# SQL mode
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
