# HireFlow ATS - Production Docker Compose Configuration
version: '3.8'

services:
  # ================================
  # Database Service
  # ================================
  mysql:
    image: mysql:8.0
    container_name: hireflow-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-hireflow_root_2024}
      MYSQL_DATABASE: ${DB_DATABASE:-hireflow_ats}
      MYSQL_USER: ${DB_USERNAME:-hireflow_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-hireflow_pass_2024}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - hireflow-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD:-hireflow_root_2024}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ================================
  # Redis Cache Service
  # ================================
  redis:
    image: redis:7-alpine
    container_name: hireflow-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - hireflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # Backend Service (Laravel API)
  # ================================
  backend:
    build:
      context: ./ats-hireflow-api/backend
      dockerfile: Dockerfile
      target: production
    container_name: hireflow-backend
    restart: unless-stopped
    environment:
      APP_NAME: "HireFlow ATS"
      APP_ENV: ${APP_ENV:-production}
      APP_KEY: ${APP_KEY}
      APP_DEBUG: ${APP_DEBUG:-false}
      APP_URL: ${APP_URL:-http://localhost}
      
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: ${DB_DATABASE:-hireflow_ats}
      DB_USERNAME: ${DB_USERNAME:-hireflow_user}
      DB_PASSWORD: ${DB_PASSWORD:-hireflow_pass_2024}
      
      CACHE_DRIVER: redis
      SESSION_DRIVER: redis
      QUEUE_CONNECTION: redis
      
      REDIS_HOST: redis
      REDIS_PASSWORD: null
      REDIS_PORT: 6379
      
      MAIL_MAILER: ${MAIL_MAILER:-smtp}
      MAIL_HOST: ${MAIL_HOST}
      MAIL_PORT: ${MAIL_PORT:-587}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      MAIL_ENCRYPTION: ${MAIL_ENCRYPTION:-tls}
      MAIL_FROM_ADDRESS: ${MAIL_FROM_ADDRESS}
      MAIL_FROM_NAME: "${MAIL_FROM_NAME:-HireFlow ATS}"
      
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-us-east-1}
      AWS_BUCKET: ${AWS_BUCKET}
      
      LOG_CHANNEL: stack
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${BACKEND_PORT:-8000}:80"
    volumes:
      - backend_storage:/var/www/html/storage/app
      - backend_logs:/var/www/html/storage/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hireflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ================================
  # Frontend Service (React SPA)
  # ================================
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: production
    container_name: hireflow-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:8000}
      VITE_APP_NAME: "HireFlow ATS"
      VITE_APP_ENV: ${APP_ENV:-production}
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    depends_on:
      - backend
    networks:
      - hireflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ================================
  # Reverse Proxy (Optional)
  # ================================
  nginx-proxy:
    image: nginx:1.25-alpine
    container_name: hireflow-proxy
    restart: unless-stopped
    ports:
      - "${PROXY_HTTP_PORT:-80}:80"
      - "${PROXY_HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/proxy/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/proxy/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - hireflow-network
    profiles:
      - proxy

# ================================
# Networks
# ================================
networks:
  hireflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================
# Volumes
# ================================
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_storage:
    driver: local
  backend_logs:
    driver: local
