# Docker Deployment Guide for ATS HireFlow

This guide provides comprehensive instructions for deploying the ATS HireFlow application using Docker containers.

## Overview

The application is containerized with a multi-service architecture:

- **Frontend**: React/Vite application served by <PERSON>inx
- **Backend**: Node.js/Express server with TypeScript
- **Database**: MySQL 8.0
- **Backup**: Automated database backup service

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers
- 10GB free disk space

## Quick Start

1. **Clone the repository and navigate to the project directory**
   ```bash
   git clone <repository-url>
   cd ats-hireflow
   ```

2. **Create environment file**
   ```bash
   cp .env.example .env.production
   ```

3. **Configure environment variables** (see Configuration section below)

4. **Build and start the application**
   ```bash
   # Production deployment
   docker-compose -f docker-compose.prod.yml up -d

   # Development deployment
   docker-compose up -d
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3000/api
   - Database: localhost:3306

## Configuration

### Environment Variables

Create a `.env.production` file with the following variables:

```env
# Application
APP_NAME="ATS HireFlow"
APP_URL=http://localhost:3000
APP_KEY=your-secret-key-here

# Database
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=ats_hireflow
DB_USERNAME=ats_user
DB_PASSWORD=secure_password_here
DB_ROOT_PASSWORD=root_password_here

# Frontend
VITE_APP_NAME="ATS HireFlow"
VITE_API_BASE_URL=http://localhost:3000/api

# Email (optional)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ATS HireFlow"

# AWS S3 (optional)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-bucket-name

# Backup
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
```

## Docker Services

### Frontend Service
- **Image**: Custom Nginx-based image
- **Port**: 3000
- **Features**:
  - Optimized production build
  - Gzip compression
  - Static file caching
  - Health checks

### Backend Service
- **Image**: Custom Node.js Alpine image
- **Port**: 3000 (internal)
- **Features**:
  - TypeScript execution with tsx
  - Production dependencies only
  - Health checks
  - Graceful shutdown handling

### Database Service
- **Image**: MySQL 8.0
- **Port**: 3306
- **Features**:
  - Persistent data storage
  - Optimized configuration
  - Health checks

### Backup Service
- **Image**: Custom backup utility
- **Features**:
  - Scheduled database backups
  - Configurable retention
  - Health monitoring

## File Structure

```
├── docker/
│   ├── nginx/
│   │   ├── nginx.conf          # Nginx main configuration
│   │   └── default.conf        # Site configuration
│   └── backup/
│       └── backup.sh           # Backup script
├── Dockerfile.frontend         # Frontend container definition
├── Dockerfile.backend          # Backend container definition
├── docker-compose.yml          # Development compose file
├── docker-compose.prod.yml     # Production compose file
└── .dockerignore               # Docker ignore patterns
```

## Management Commands

### Build Services
```bash
# Build all services
docker-compose -f docker-compose.prod.yml build

# Build specific service
docker-compose -f docker-compose.prod.yml build frontend
docker-compose -f docker-compose.prod.yml build backend
```

### Start/Stop Services
```bash
# Start all services
docker-compose -f docker-compose.prod.yml up -d

# Stop all services
docker-compose -f docker-compose.prod.yml down

# Restart specific service
docker-compose -f docker-compose.prod.yml restart backend
```

### View Logs
```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
```

### Database Management
```bash
# Access MySQL shell
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# Create database backup
docker-compose -f docker-compose.prod.yml exec backup /backup.sh

# Restore from backup
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p ats_hireflow < backup.sql
```

## Health Checks

All services include health checks:

- **Frontend**: HTTP check on port 80
- **Backend**: HTTP check on `/api/ping`
- **Database**: MySQL connection test
- **Backup**: Script execution test

Check service health:
```bash
docker-compose -f docker-compose.prod.yml ps
```

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   
   # Kill process if needed
   kill -9 <PID>
   ```

2. **Database connection issues**
   ```bash
   # Check database logs
   docker-compose -f docker-compose.prod.yml logs mysql
   
   # Verify database is running
   docker-compose -f docker-compose.prod.yml exec mysql mysqladmin ping
   ```

3. **Build failures**
   ```bash
   # Clean build cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose -f docker-compose.prod.yml build --no-cache
   ```

### Performance Optimization

1. **Resource limits**: Adjust memory and CPU limits in docker-compose.yml
2. **Volume optimization**: Use named volumes for better performance
3. **Network optimization**: Use custom networks for service isolation

## Security Considerations

1. **Environment variables**: Never commit `.env` files to version control
2. **Database passwords**: Use strong, unique passwords
3. **Network security**: Configure firewall rules for production
4. **SSL/TLS**: Use reverse proxy with SSL certificates in production
5. **Updates**: Regularly update base images and dependencies

## Production Deployment

For production deployment:

1. Use a reverse proxy (Nginx/Traefik) with SSL certificates
2. Set up monitoring and logging
3. Configure automated backups
4. Implement health monitoring
5. Set up CI/CD pipeline for automated deployments

## Support

For issues and questions:
- Check the troubleshooting section above
- Review Docker and application logs
- Consult the main project documentation
