# HireFlow ATS - Production Docker Compose Override
version: "3.8"

services:
  # ================================
  # Production MySQL Configuration
  # ================================
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./backups/mysql:/backups:ro
    command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=512M
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"

  # ================================
  # Production Redis Configuration
  # ================================
  redis:
    image: redis:7.2-alpine
    volumes:
      - redis_prod_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD:-}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # ================================
  # Production Backend Configuration
  # ================================
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    environment:
      APP_ENV: production
      APP_DEBUG: false
      APP_KEY: ${APP_KEY}
      APP_URL: ${APP_URL}

      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: ${DB_DATABASE}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}

      CACHE_DRIVER: redis
      SESSION_DRIVER: redis
      QUEUE_CONNECTION: redis

      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      REDIS_PORT: 6379

      MAIL_MAILER: ${MAIL_MAILER}
      MAIL_HOST: ${MAIL_HOST}
      MAIL_PORT: ${MAIL_PORT}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      MAIL_ENCRYPTION: ${MAIL_ENCRYPTION}
      MAIL_FROM_ADDRESS: ${MAIL_FROM_ADDRESS}
      MAIL_FROM_NAME: ${MAIL_FROM_NAME}

      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
      AWS_BUCKET: ${AWS_BUCKET}

      LOG_CHANNEL: stack
      LOG_LEVEL: warning
    volumes:
      - backend_prod_storage:/var/www/html/storage/app
      - backend_prod_logs:/var/www/html/storage/logs
      - ./backups/backend:/backups:rw
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # ================================
  # Production Frontend Configuration
  # ================================
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    environment:
      VITE_API_BASE_URL: ${VITE_API_BASE_URL}
      VITE_APP_NAME: ${VITE_APP_NAME}
      VITE_APP_ENV: production
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 256M
          cpus: "0.5"
        reservations:
          memory: 128M
          cpus: "0.25"
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # ================================
  # Production Reverse Proxy
  # ================================
  nginx-proxy:
    image: nginx:1.25-alpine
    volumes:
      - ./docker/proxy/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/proxy/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx:rw
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: "0.5"
        reservations:
          memory: 128M
          cpus: "0.25"
    profiles:
      - proxy

  # ================================
  # Backup Service
  # ================================
  backup:
    image: alpine:3.18
    container_name: hireflow-backup
    restart: unless-stopped
    environment:
      DB_HOST: mysql
      DB_DATABASE: ${DB_DATABASE}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      BACKUP_SCHEDULE: "0 2 * * *" # Daily at 2 AM
      BACKUP_RETENTION_DAYS: 30
    volumes:
      - mysql_prod_data:/var/lib/mysql:ro
      - backend_prod_storage:/var/www/html/storage:ro
      - ./backups:/backups:rw
      - ./scripts/backup.sh:/backup.sh:ro
    command: sh -c "apk add --no-cache mysql-client dcron && echo '${BACKUP_SCHEDULE} /backup.sh' | crontab - && crond -f"
    depends_on:
      - mysql
      - backend
    networks:
      - hireflow-network
    profiles:
      - backup

# ================================
# Production Volumes
# ================================
volumes:
  mysql_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/docker/volumes/hireflow_mysql_prod/_data
  redis_prod_data:
    driver: local
  backend_prod_storage:
    driver: local
  backend_prod_logs:
    driver: local
