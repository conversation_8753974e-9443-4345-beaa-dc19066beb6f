# HireFlow ATS Backend - Production Docker Image
# Multi-stage build for optimized Laravel application

# ================================
# Stage 1: Composer Dependencies
# ================================
FROM php:8.2-cli-alpine AS composer-stage

# Install system dependencies and PHP extensions needed for composer
RUN apk add --no-cache \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    oniguruma-dev \
    icu-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        gd \
        zip \
        mbstring \
        intl \
        bcmath

# Install Composer
COPY --from=composer:2.8 /usr/bin/composer /usr/bin/composer

WORKDIR /app

# Copy composer files first for better layer caching
COPY composer.json composer.lock ./

# Install PHP dependencies (production only)
RUN composer install \
    --no-dev \
    --no-scripts \
    --no-autoloader \
    --optimize-autoloader \
    --prefer-dist \
    --no-interaction

# ================================
# Stage 2: Production PHP Image
# ================================
FROM php:8.2-fpm-alpine AS production

# Install system dependencies and PHP extensions
RUN apk add --no-cache \
    nginx \
    supervisor \
    mysql-client \
    redis \
    imagemagick \
    imagemagick-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    oniguruma-dev \
    icu-dev \
    # Build dependencies for PECL
    autoconf \
    gcc \
    g++ \
    make \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mysqli \
        gd \
        zip \
        mbstring \
        intl \
        opcache \
        bcmath \
        exif \
    && pecl install redis imagick \
    && docker-php-ext-enable redis imagick \
    # Clean up build dependencies
    && apk del autoconf gcc g++ make \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Create application user
RUN addgroup -g 1000 -S www && \
    adduser -u 1000 -S www -G www

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY --chown=www:www . .

RUN chown -R www:www /var/www/html /var/lib/nginx /var/log/nginx
# Copy Composer dependencies from composer stage
COPY --from=composer-stage --chown=www:www /app/vendor ./vendor

# Copy Composer binary for autoloader generation
COPY --from=composer:2.8 /usr/bin/composer /usr/bin/composer

# Create public directory structure
RUN mkdir -p public/build public/storage

# Generate optimized autoloader
RUN composer dump-autoload --optimize --classmap-authoritative

# Create necessary directories and set permissions
RUN mkdir -p \
    storage/logs \
    storage/framework/cache \
    storage/framework/sessions \
    storage/framework/views \
    storage/app/public \
    bootstrap/cache \
    && chown -R www:www storage bootstrap/cache \
    && chmod -R 775 storage bootstrap/cache

# Copy configuration files
COPY .docker/web/php81nginx/php.ini /usr/local/etc/php/conf.d/99-custom.ini
COPY .docker/web/php81nginx/config/nginx.conf /etc/nginx/nginx.conf
COPY .docker/web/php81nginx/config/conf.d/default.conf /etc/nginx/http.d/default.conf
COPY .docker/web/php81nginx/config/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create storage link
RUN php artisan storage:link || true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/up || exit 1

# Expose port
EXPOSE 8080

# Switch to non-root user
USER www

# Start supervisor (manages nginx + php-fpm)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
