[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0
pidfile=/run/supervisord.pid

[program:php-fpm]
command=php-fpm -F
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=false
startretries=0


[program:nginx]
command=nginx -g 'daemon off;'
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=false
startretries=0


# [program:laravel-worker-ssl]
# process_name=%(program_name)s_%(process_num)02d
# command=php /www/artisan horizon
# autostart=true
# autorestart=true
# startsecs=0
# #numprocs=%(ENV_NUMBER_OF_WORKER)s
# redirect_stderr=true
# user=nobody

# [program:laravel-scheduler]
# process_name=%(program_name)s_%(process_num)02d
# command=/bin/sh -c "while [ true ]; do (php /www/artisan schedule:run --verbose --no-interaction &); sleep 60; done"
# autostart=true
# autorestart=true
# numprocs=1
# user=nobody
# redirect_stderr=true
