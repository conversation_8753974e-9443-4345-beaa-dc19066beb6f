import { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DroppableStageColumn } from "@/components/pipeline/DroppableStageColumn";
import MessageForm from "@/components/messages/MessageForm";
import { CandidateDetailModal } from "@/components/candidates/CandidateDetailModal";
import { CandidateModal } from "@/components/candidates/EditCandidateModal";
import {
  GitBranch,
  Settings,
  MoreHorizontal,
  Calendar,
  TrendingUp,
  Users,
  Mail,
  Plus,
  Filter,
  BarChart3,
  Clock,
  Target,
  Zap,
  Download,
  RefreshCw,
} from "lucide-react";
import { mockCandidates, Candidate } from "@/data/mockData";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { useCandidates, useUpdateCandidateStatus, useJobs } from "@/hooks/useApi";
import { ProtectedRoute } from "@/lib/auth";
import { useSimplePageTitle } from "@/hooks/usePageTitle";

export default function Pipeline() {
  const { t } = useTranslation();

  // Set page title
  //useSimplePageTitle("pageTitle.pipeline.kanban");
  useSimplePageTitle("nav.pipeline");
  const [selectedJob, setSelectedJob] = useState<string>("all");

  // API hooks
  const { data: candidatesData, isLoading } = useCandidates();
  const updateCandidateStatusMutation = useUpdateCandidateStatus();
  const { data: jobsData, isLoading: jobsLoading, error: jobsError } = useJobs({
    status: "active",
    per_page: 100,
    sort: "title",
  });

  // Extract active jobs
  const activeJobs = jobsData?.data || [];

  const pipelineStages = [
    {
      id: "sourced",
      name: t.pipeline.sourced,
      color: "bg-gray-100",
      description: "Xác định ứng viên ban đầu",
    },
    {
      id: "applied",
      name: t.pipeline.applied,
      color: "bg-blue-100",
      description: "Đơn ứng tuyển chính thức đã gửi",
    },
    {
      id: "screening",
      name: t.pipeline.screening,
      color: "bg-yellow-100",
      description: "Xem xét và sàng lọc ban đầu",
    },
    {
      id: "interview",
      name: t.pipeline.interview,
      color: "bg-purple-100",
      description: "Phỏng vấn kỹ thuật và văn hóa",
    },
    {
      id: "offer",
      name: t.pipeline.offer,
      color: "bg-orange-100",
      description: "Đề nghị công việc đã gửi",
    },
    {
      id: "hired",
      name: t.pipeline.hired,
      color: "bg-green-100",
      description: "Tuyển dụng thành công",
    },
    {
      id: "rejected",
      name: t.pipeline.rejected,
      color: "bg-red-100",
      description: "Không tiếp tục",
    },
  ];
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailCandidates, setEmailCandidates] = useState<Candidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [addToStage, setAddToStage] = useState<Candidate["status"]>("sourced");
  const [viewMode, setViewMode] = useState<"board" | "analytics">("board");

  // Get candidates from API
  const candidates = candidatesData?.data || [];

  // Filter candidates based on selected job
  const filteredCandidates = useMemo(() => {
    return selectedJob === "all"
      ? candidates
      : candidates.filter((c) => c.job_posting_id?.toString() === selectedJob);
  }, [selectedJob, candidates]);

  // Group candidates by stage
  const candidatesByStage = useMemo(() => {
    return pipelineStages.reduce(
      (acc, stage) => {
        acc[stage.id] = filteredCandidates.filter((c) => c.status === stage.id);
        return acc;
      },
      {} as Record<string, Candidate[]>,
    );
  }, [filteredCandidates]);

  // Calculate pipeline metrics
  const pipelineMetrics = useMemo(() => {
    const total = filteredCandidates.length;
    const hired = candidatesByStage.hired?.length || 0;
    const rejected = candidatesByStage.rejected?.length || 0;
    const active = total - hired - rejected;

    const conversionRates = {
      appliedToScreening:
        candidatesByStage.applied?.length > 0
          ? (
              ((candidatesByStage.screening?.length || 0) /
                candidatesByStage.applied.length) *
              100
            ).toFixed(1)
          : "0",
      screeningToInterview:
        candidatesByStage.screening?.length > 0
          ? (
              ((candidatesByStage.interview?.length || 0) /
                candidatesByStage.screening.length) *
              100
            ).toFixed(1)
          : "0",
      interviewToOffer:
        candidatesByStage.interview?.length > 0
          ? (
              ((candidatesByStage.offer?.length || 0) /
                candidatesByStage.interview.length) *
              100
            ).toFixed(1)
          : "0",
      offerToHire:
        candidatesByStage.offer?.length > 0
          ? ((hired / candidatesByStage.offer.length) * 100).toFixed(1)
          : "0",
    };

    return {
      total,
      active,
      hired,
      rejected,
      conversionRates,
      hireRate: total > 0 ? ((hired / total) * 100).toFixed(1) : "0",
    };
  }, [filteredCandidates, candidatesByStage]);

  const handleDrop = async (
    candidateId: string,
    newStatus: Candidate["status"],
  ) => {
    const candidate = candidates.find((c) => c.id?.toString() === candidateId);
    if (!candidate) return;

    try {
      await updateCandidateStatusMutation.mutateAsync({
        id: candidateId,
        status: newStatus,
        notes: `Moved to ${newStatus} via pipeline`,
      });

      toast.success(
        `Đã chuyển ${candidate.name} sang ${newStatus
          .charAt(0)
          .toUpperCase()}${newStatus.slice(1)}`,
      );
    } catch (error) {
      toast.error(`Không thể cập nhật trạng thái cho ${candidate.name}`);
      console.error("Status update error:", error);
    }
  };

  const handleStatusChange = async (
    candidateId: string,
    newStatus: Candidate["status"],
  ) => {
    await handleDrop(candidateId, newStatus);
  };

  const handleSendEmail = (candidate: Candidate) => {
    setEmailCandidates([candidate]);
    setIsEmailModalOpen(true);
  };

  const handleSendBulkEmail = (candidates: Candidate[]) => {
    if (candidates.length === 0) {
      toast.error("Không có ứng viên nào trong giai đoạn này để gửi email");
      return;
    }
    setEmailCandidates(candidates);
    setIsEmailModalOpen(true);
  };

  const handleViewDetails = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsDetailModalOpen(true);
  };

  const handleAddCandidate = (status: Candidate["status"]) => {
    setAddToStage(status);
    setIsAddModalOpen(true);
  };

  const handleAddCandidateSubmit = async (
    newCandidate: Omit<Candidate, "id">,
  ) => {
    // This should trigger the API to create a new candidate
    // The component will automatically refresh via the useCandidates hook
    try {
      // Note: This would typically use a createCandidate mutation
      // For now, just show success message and close modal
      toast.success("Candidate added successfully");
      setIsAddModalOpen(false);
    } catch (error) {
      toast.error("Failed to add candidate");
    }
  };

  const handleBulkAction = (action: string) => {
    switch (action) {
      case "email-all":
        handleSendBulkEmail(filteredCandidates);
        break;
      case "export":
        exportPipelineData();
        break;
      case "refresh":
        toast.info("Quy trình đã được làm mới");
        break;
      case "auto-advance":
        toast.info("Quy t��c tự động tiến đã được cấu hình");
        break;
      default:
        toast.info(`Hành động: ${action}`);
    }
  };

  const exportPipelineData = () => {
    const pipelineData = {
      job:
        selectedJob === "all"
          ? "All Jobs"
          : activeJobs.find((j) => j.id.toString() === selectedJob)?.title,
      exportDate: new Date().toISOString(),
      metrics: pipelineMetrics,
      stages: pipelineStages.map((stage) => ({
        name: stage.name,
        count: candidatesByStage[stage.id]?.length || 0,
        candidates: candidatesByStage[stage.id] || [],
      })),
    };

    const blob = new Blob([JSON.stringify(pipelineData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pipeline_export_${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Dữ liệu quy trình đã được xuất!");
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                {t.pipeline.title}
              </h1>
              <p className="text-muted-foreground">{t.pipeline.subtitle}</p>
            </div>
            <div className="flex gap-2 hidden">
              <Button
                variant="outline"
                className="gap-2 rounded-xl"
                onClick={() => handleBulkAction("refresh")}
              >
                <RefreshCw className="w-4 h-4" />
                {t.common.refresh}
              </Button>
              <Button
                variant="outline"
                className="gap-2 rounded-xl"
                onClick={() => handleBulkAction("export")}
              >
                <Download className="w-4 h-4" />
                {t.common.export}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2 rounded-xl">
                    <MoreHorizontal className="w-4 h-4" />
                    {t.common.actions}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="rounded-xl">
                  <DropdownMenuLabel>{t.pipeline.actions}</DropdownMenuLabel>
                  <DropdownMenuItem
                    onClick={() => handleBulkAction("email-all")}
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    {t.pipeline.bulkEmail}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleBulkAction("auto-advance")}
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    {t.pipeline.automations}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    {t.pipeline.stageSettings}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Select value={selectedJob} onValueChange={setSelectedJob}>
                <SelectTrigger className="w-[300px] rounded-xl">
                  <SelectValue placeholder={jobsLoading ? "Đang tải..." : "Chọn vị trí công việc"} />
                </SelectTrigger>
                <SelectContent className="rounded-xl">
                  <SelectItem value="all">
                    {t.common.all} {t.jobs.title}
                  </SelectItem>
                  {jobsLoading ? (
                    <SelectItem value="" disabled>
                      Đang tải danh sách công việc...
                    </SelectItem>
                  ) : (
                    activeJobs.map((job) => (
                      <SelectItem key={job.id} value={job.id.toString()}>
                        {job.title} ({job.department})
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>

              <div className="flex gap-1 border rounded-xl">
                <Button
                  variant={viewMode === "board" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("board")}
                  className="rounded-l-xl rounded-r-none gap-2"
                >
                  <GitBranch className="h-4 w-4" />
                  {t.pipeline.dragDrop}
                </Button>
                <Button
                  variant={viewMode === "analytics" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("analytics")}
                  className="rounded-r-xl rounded-l-none gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  {t.pipeline.analytics}
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              {selectedJob !== "all" && (
                <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium">
                  <Filter className="w-3 h-3" />
                  Lọc theo: {activeJobs.find((j) => j.id.toString() === selectedJob)?.title || "Công việc"}
                </div>
              )}
              <span>
                {t.common.total} {t.candidates.title}: {pipelineMetrics.total}
              </span>
              <span>
                {t.common.active}: {pipelineMetrics.active}
              </span>
              <span>
                {t.status.hired}: {pipelineMetrics.hired}
              </span>
              <span>Tỷ lệ tuyển dụng: {pipelineMetrics.hireRate}%</span>
            </div>
          </div>

          {viewMode === "board" ? (
            <>
              {/* Pipeline Board */}
              <div className="bg-card border border-border rounded-xl p-6">
                <div className="flex gap-6 overflow-x-auto pb-4">
                  {pipelineStages.map((stage) => (
                    <DroppableStageColumn
                      key={stage.id}
                      stage={stage}
                      candidates={candidatesByStage[stage.id] || []}
                      onDrop={handleDrop}
                      onStatusChange={handleStatusChange}
                      onSendEmail={handleSendEmail}
                      onSendBulkEmail={handleSendBulkEmail}
                      onViewDetails={handleViewDetails}
                      onAddCandidate={handleAddCandidate}
                    />
                  ))}
                </div>
              </div>

              {/* Pipeline Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/20 rounded-xl">
                        <Target className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Conversion Rate
                        </p>
                        <p className="text-2xl font-bold">
                          {pipelineMetrics.conversionRates.appliedToScreening}%
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Applied to Screening
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-emerald-500/20 bg-gradient-to-br from-emerald-500/5 to-green-500/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-emerald-500/20 rounded-xl">
                        <TrendingUp className="h-5 w-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Offer Acceptance
                        </p>
                        <p className="text-2xl font-bold">
                          {pipelineMetrics.conversionRates.offerToHire}%
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Offers Accepted
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-500/20 rounded-xl">
                        <Clock className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Time in Pipeline
                        </p>
                        <p className="text-2xl font-bold">16 days</p>
                        <p className="text-xs text-muted-foreground">
                          Average Duration
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-red-500/5">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-orange-500/20 rounded-xl">
                        <Users className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Interview Rate
                        </p>
                        <p className="text-2xl font-bold">
                          {pipelineMetrics.conversionRates.screeningToInterview}
                          %
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Screening to Interview
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            /* Analytics View */
            <div className="space-y-6">
              {/* Conversion Funnel */}

              <Card>
                <CardHeader>
                  <CardTitle>Phân tích phễu chuyển đổi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {pipelineStages.slice(0, -1).map((stage, index) => {
                      const nextStage = pipelineStages[index + 1];
                      const currentCount =
                        candidatesByStage[stage.id]?.length || 0;
                      const nextCount =
                        candidatesByStage[nextStage.id]?.length || 0;
                      const conversionRate =
                        currentCount > 0
                          ? ((nextCount / currentCount) * 100).toFixed(1)
                          : "0";

                      return (
                        <div
                          key={stage.id}
                          className="flex items-center justify-between p-4 bg-muted/50 rounded-lg"
                        >
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <p className="font-bold text-lg">
                                {currentCount}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {stage.name}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <div className="w-12 h-0.5 bg-border"></div>
                              <span className="text-xs">{conversionRate}%</span>
                              <div className="w-12 h-0.5 bg-border"></div>
                            </div>
                            <div className="text-center">
                              <p className="font-bold text-lg">{nextCount}</p>
                              <p className="text-sm text-muted-foreground">
                                {nextStage.name}
                              </p>
                            </div>
                          </div>
                          <Badge
                            variant={
                              parseFloat(conversionRate) > 50
                                ? "default"
                                : "secondary"
                            }
                          >
                            {conversionRate}% conversion
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Stage Performance */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Stage Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {pipelineStages.map((stage) => {
                        const count = candidatesByStage[stage.id]?.length || 0;
                        const percentage =
                          pipelineMetrics.total > 0
                            ? (count / pipelineMetrics.total) * 100
                            : 0;

                        return (
                          <div key={stage.id} className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>{stage.name}</span>
                              <span className="font-medium">
                                {count} ({percentage.toFixed(1)}%)
                              </span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${stage.color.replace("bg-", "bg-").replace("-100", "-500")}`}
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Pipeline Health Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-primary mb-2">
                          {(
                            (parseFloat(
                              pipelineMetrics.conversionRates
                                .appliedToScreening,
                            ) +
                              parseFloat(
                                pipelineMetrics.conversionRates
                                  .screeningToInterview,
                              ) +
                              parseFloat(
                                pipelineMetrics.conversionRates
                                  .interviewToOffer,
                              ) +
                              parseFloat(
                                pipelineMetrics.conversionRates.offerToHire,
                              )) /
                            4
                          ).toFixed(0)}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Overall Health Score
                        </p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Pipeline Velocity</span>
                          <Badge variant="default">Good</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Conversion Quality</span>
                          <Badge variant="default">Excellent</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Bottleneck Analysis</span>
                          <Badge variant="secondary">Screening</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Modals */}
          <MessageForm
            isOpen={isEmailModalOpen}
            onClose={() => setIsEmailModalOpen(false)}
            mode="compose"
            selectedCandidates={emailCandidates}
          />

          <CandidateDetailModal
            candidate={selectedCandidate}
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedCandidate(null);
            }}
          />

          <CandidateModal
            mode="add"
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onCreate={handleAddCandidateSubmit}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
