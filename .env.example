# HireFlow ATS - Docker Environment Configuration

# ================================
# Application Settings
# ================================
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your-32-character-secret-key-here
APP_URL=http://localhost

# ================================
# Database Configuration
# ================================
DB_DATABASE=hireflow_ats
DB_USERNAME=hireflow_user
DB_PASSWORD=hireflow_pass_2024
DB_ROOT_PASSWORD=hireflow_root_2024
DB_PORT=3306

# ================================
# Redis Configuration
# ================================
REDIS_PORT=6379

# ================================
# Service Ports
# ================================
BACKEND_PORT=8000
FRONTEND_PORT=3000
PROXY_HTTP_PORT=80
PROXY_HTTPS_PORT=443

# ================================
# Mail Configuration
# ================================
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="HireFlow ATS"

# ================================
# AWS S3 Configuration (Optional)
# ================================
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=hireflow-storage

# ================================
# Frontend Configuration
# ================================
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_NAME="HireFlow ATS"
VITE_APP_ENV=production

# ================================
# Logging
# ================================
LOG_LEVEL=info

# ================================
# SSL Configuration (Optional)
# ================================
# SSL_CERT_PATH=./docker/ssl/cert.pem
# SSL_KEY_PATH=./docker/ssl/key.pem
