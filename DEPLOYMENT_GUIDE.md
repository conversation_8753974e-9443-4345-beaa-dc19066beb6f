# HireFlow ATS - Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the HireFlow ATS application using Docker containers in both development and production environments.

## 🔧 Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+), macOS, or Windows with WSL2
- **Docker**: Version 24.0 or higher
- **Docker Compose**: Version 2.0 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended for production)
- **Storage**: Minimum 20GB free disk space
- **Network**: Internet connection for downloading images and dependencies

### Software Installation
```bash
# Install Docker (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

## 🚀 Development Deployment

### 1. <PERSON>lone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd ats-hireflow

# Copy environment configuration
cp .env.example .env
```

### 2. Configure Environment
Edit the `.env` file with your development settings:
```bash
# Application
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# Database
DB_DATABASE=hireflow_ats_dev
DB_USERNAME=hireflow_dev
DB_PASSWORD=dev_password_123

# Ports
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

### 3. Generate Application Key
```bash
# Generate Laravel application key
docker run --rm -v $(pwd)/ats-hireflow-api/backend:/app composer:2.8 \
  sh -c "cd /app && php artisan key:generate --show"

# Copy the generated key to your .env file
# APP_KEY=base64:your-generated-key-here
```

### 4. Build and Start Services
```bash
# Build all services
docker-compose build

# Start services in detached mode
docker-compose up -d

# Check service status
docker-compose ps
```

### 5. Initialize Database
```bash
# Run database migrations
docker-compose exec backend php artisan migrate

# Seed the database with sample data
docker-compose exec backend php artisan db:seed

# Create storage link
docker-compose exec backend php artisan storage:link
```

### 6. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api/v1
- **API Documentation**: http://localhost:8000/api/documentation

### 7. Development Commands
```bash
# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Restart services
docker-compose restart backend frontend

# Stop all services
docker-compose down
```

## 🏭 Production Deployment

### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y curl wget git unzip

# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Environment Configuration
```bash
# Copy production environment template
cp .env.example .env.production

# Edit production environment
nano .env.production
```

Production environment example:
```bash
# Application
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your-production-key-here
APP_URL=https://your-domain.com

# Database
DB_DATABASE=hireflow_ats_prod
DB_USERNAME=hireflow_prod
DB_PASSWORD=secure_production_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# AWS S3 (Optional)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=hireflow-production

# Frontend
VITE_API_BASE_URL=https://api.your-domain.com
```

### 3. SSL Certificate Setup (Optional)
```bash
# Create SSL directory
mkdir -p docker/ssl

# Generate self-signed certificate (for testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/ssl/key.pem \
  -out docker/ssl/cert.pem

# Or copy your SSL certificates
cp your-cert.pem docker/ssl/cert.pem
cp your-key.pem docker/ssl/key.pem
```

### 4. Production Build and Deploy
```bash
# Build production images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Run production setup
docker-compose exec backend php artisan migrate --force
docker-compose exec backend php artisan config:cache
docker-compose exec backend php artisan route:cache
docker-compose exec backend php artisan view:cache
docker-compose exec backend php artisan optimize
```

### 5. Enable Reverse Proxy (Optional)
```bash
# Start with reverse proxy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile proxy up -d
```

### 6. Enable Backup Service
```bash
# Start with backup service
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile backup up -d

# Manual backup
docker-compose exec backup /backup.sh
```

## 🔍 Monitoring and Maintenance

### Health Checks
```bash
# Check service health
curl http://localhost:8000/up  # Backend health
curl http://localhost:3000/health  # Frontend health

# Check container status
docker-compose ps
docker stats
```

### Log Management
```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# View specific service logs
docker-compose logs --tail=100 mysql
docker-compose logs --tail=100 redis

# Log rotation (add to crontab)
0 2 * * * docker system prune -f
```

### Database Management
```bash
# Database backup
docker-compose exec mysql mysqldump -u root -p hireflow_ats > backup.sql

# Database restore
docker-compose exec -T mysql mysql -u root -p hireflow_ats < backup.sql

# Access MySQL shell
docker-compose exec mysql mysql -u root -p
```

### Performance Optimization
```bash
# Clear application cache
docker-compose exec backend php artisan cache:clear
docker-compose exec backend php artisan config:clear
docker-compose exec backend php artisan route:clear
docker-compose exec backend php artisan view:clear

# Optimize for production
docker-compose exec backend php artisan optimize
docker-compose exec backend php artisan config:cache
docker-compose exec backend php artisan route:cache
docker-compose exec backend php artisan view:cache
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Permission Errors
```bash
# Fix storage permissions
docker-compose exec backend chown -R www:www storage bootstrap/cache
docker-compose exec backend chmod -R 775 storage bootstrap/cache
```

#### 2. Database Connection Issues
```bash
# Check MySQL status
docker-compose exec mysql mysqladmin ping -u root -p

# Restart MySQL
docker-compose restart mysql

# Check database logs
docker-compose logs mysql
```

#### 3. Frontend Build Issues
```bash
# Rebuild frontend
docker-compose build frontend --no-cache

# Check frontend logs
docker-compose logs frontend
```

#### 4. Memory Issues
```bash
# Check container resource usage
docker stats

# Increase memory limits in docker-compose.prod.yml
# Add under service configuration:
deploy:
  resources:
    limits:
      memory: 2G
```

### Service Recovery
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart backend

# Force recreate containers
docker-compose up -d --force-recreate

# Complete reset (WARNING: This will delete all data)
docker-compose down -v
docker system prune -a
```

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose build
docker-compose up -d

# Run migrations
docker-compose exec backend php artisan migrate
```

### Security Updates
```bash
# Update base images
docker-compose pull
docker-compose up -d

# Update system packages in containers
docker-compose build --no-cache
```

### Backup and Recovery
```bash
# Manual backup
./scripts/backup.sh

# Restore from backup
# 1. Stop services
docker-compose down

# 2. Restore database
docker-compose up -d mysql
docker-compose exec -T mysql mysql -u root -p hireflow_ats < backups/mysql/backup.sql

# 3. Restore files
tar -xzf backups/backend/storage_backup.tar.gz -C volumes/backend_storage/

# 4. Start all services
docker-compose up -d
```

This deployment guide provides comprehensive instructions for successfully deploying and maintaining the HireFlow ATS application in both development and production environments.
